<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI搜索导航</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .search-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .search-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s;
            cursor: move;
            user-select: none;
            position: relative;
        }
        .search-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .search-item.dragging {
            opacity: 0.5;
            background: #e9ecef;
        }
        .search-item a {
            color: #333;
            text-decoration: none;
            font-size: 18px;
            display: block;
        }
        .search-item a:hover {
            color: #007bff;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .description {
            font-size: 14px;
            color: #666;
            margin-top: 10px;
        }
        .reset-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #dc3545;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .reset-button:hover {
            background-color: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 添加按钮样式 */
        .add-button {
            position: fixed;
            bottom: 20px;
            right: 150px; /* 位于重置按钮左侧 */
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .add-button:hover {
            background-color: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            width: 50%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .close {
            position: absolute;
            right: 20px;
            top: 10px;
            font-size: 28px;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .submit-btn {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        .submit-btn:hover {
            background-color: #218838;
        }

        /* 添加拖拽位置指示器样式 */
        .search-item.drag-over-top {
            border-top: 2px solid #007bff;
        }
        
        .search-item.drag-over-bottom {
            border-bottom: 2px solid #007bff;
        }

        .delete-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #dc3545;
            color: white;
            border: none;
            cursor: pointer;
            display: none;
            font-size: 14px;
            line-height: 24px;
            padding: 0;
            text-align: center;
        }
        
        .search-item:hover .delete-btn {
            display: block;
        }
        
        .delete-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <h1>AI搜索导航</h1>
    <div class="search-container" id="searchContainer">
        <div class="search-item" draggable="true">
            <a href="https://www.tiangong.cn/" target="_blank">天工AI</a>
            <div class="description">智能搜索与对话平台</div>
        </div>
        
        <div class="search-item" draggable="true">
            <a href="https://www.blackbox.ai/" target="_blank">Blackbox搜索</a>
            <div class="description">AI驱动的智能搜索引擎</div>
        </div>
        
        <div class="search-item" draggable="true">
            <a href="https://www.perplexity.ai/" target="_blank">Perplexity搜索</a>
            <div class="description">智能问答搜索引擎</div>
        </div>
        
        <div class="search-item" draggable="true">
            <a href="https://metaso.cn/" target="_blank">秘塔AI搜索</a>
            <div class="description">中文智能搜索引擎</div>
        </div>
        
        <div class="search-item" draggable="true">
            <a href="https://yuanbao.tencent.com/" target="_blank">腾讯元宝</a>
            <div class="description">腾讯AI搜索平台</div>
        </div>
        
        <div class="search-item" draggable="true">
            <a href="https://www.n.cn/" target="_blank">纳米搜索</a>
            <div class="description">智能化搜索引擎</div>
        </div>
        
        <div class="search-item" draggable="true">
            <a href="https://kimi.moonshot.cn/" target="_blank">Kimi搜索</a>
            <div class="description">AI智能助手搜索</div>
        </div>
        
        <div class="search-item" draggable="true">
            <a href="https://chat.deepseek.com/" target="_blank">Deepseek搜索</a>
            <div class="description">深度AI搜索引擎</div>
        </div>

        <div class="search-item" draggable="true">
            <a href="https://www.doubao.com/chat/search" target="_blank">豆包AI搜索</a>
            <div class="description">豆包AI搜索引擎</div>
        </div>
        
        <div class="search-item" draggable="true">
            <a href="https://think.baidu.com/" target="_blank">百度AI搜索</a>
            <div class="description">百度AI搜索引擎</div>
        </div>
        
    </div>

    <button class="reset-button" id="resetButton">重置排序</button>
    <button class="add-button" id="addButton">添加链接</button>

    <!-- 添加模态框 -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>添加新链接</h2>
            <form id="addLinkForm">
                <div class="form-group">
                    <label for="siteName">网站名称:</label>
                    <input type="text" id="siteName" required>
                </div>
                <div class="form-group">
                    <label for="siteUrl">网址:</label>
                    <input type="url" id="siteUrl" required>
                </div>
                <div class="form-group">
                    <label for="siteDescription">描述:</label>
                    <input type="text" id="siteDescription" required>
                </div>
                <button type="submit" class="submit-btn">添加</button>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('searchContainer');
            const items = document.querySelectorAll('.search-item');
            const resetButton = document.getElementById('resetButton');
            
            let draggedItem = null;

            // 保存初始排序
            const initialOrder = Array.from(items).map(item => ({
                href: item.querySelector('a').href,
                text: item.querySelector('a').textContent,
                description: item.querySelector('.description').textContent
            }));

            // 重置函数
            function resetOrder() {
                if (confirm('确定要重置排序吗？这将删除所有新添加的链接！')) {
                    container.innerHTML = ''; // 清空容器
                    
                    initialOrder.forEach(itemData => {
                        const newItem = createSearchItem(itemData);
                        container.appendChild(newItem);
                    });

                    // 保存重置后的状态
                    saveOrder();
                    // 重新绑定拖拽事件
                    bindDragEvents();
                }
            }

            // 绑定重置按钮点击事件
            resetButton.addEventListener('click', resetOrder);

            // 修改保存函数，保存所有链接包括新添加的
            function saveOrder() {
                const currentItems = container.querySelectorAll('.search-item');
                const orderArray = Array.from(currentItems).map(item => ({
                    href: item.querySelector('a').href,
                    text: item.querySelector('a').textContent,
                    description: item.querySelector('.description').textContent
                }));
                // 同时保存初始数据和新添加的数据
                localStorage.setItem('allSearchItems', JSON.stringify(orderArray));
            }

            // 修改加载函数，加载所有保存的链接
            function loadOrder() {
                const savedItems = localStorage.getItem('allSearchItems');
                if (savedItems) {
                    const itemsArray = JSON.parse(savedItems);
                    container.innerHTML = ''; // 清空容器
                    
                    itemsArray.forEach(itemData => {
                        const newItem = createSearchItem(itemData);
                        container.appendChild(newItem);
                    });
                    
                    // 重新绑定拖拽事件
                    bindDragEvents();
                }
            }

            // 重新绑定事件（因为DOM可能被重建）
            function bindDragEvents() {
                const currentItems = container.querySelectorAll('.search-item');
                
                currentItems.forEach(item => {
                    item.addEventListener('dragstart', function(e) {
                        draggedItem = this;
                        this.classList.add('dragging');
                    });

                    item.addEventListener('dragend', function(e) {
                        this.classList.remove('dragging');
                        // 保存新的排序
                        saveOrder();
                    });

                    item.addEventListener('dragover', function(e) {
                        e.preventDefault();
                        // 添加位置指示
                        const rect = this.getBoundingClientRect();
                        const midY = rect.top + rect.height / 2;
                        if (e.clientY < midY) {
                            this.classList.add('drag-over-top');
                            this.classList.remove('drag-over-bottom');
                        } else {
                            this.classList.add('drag-over-bottom');
                            this.classList.remove('drag-over-top');
                        }
                    });

                    item.addEventListener('dragleave', function(e) {
                        this.classList.remove('drag-over-top', 'drag-over-bottom');
                    });

                    item.addEventListener('drop', function(e) {
                        e.preventDefault();
                        this.classList.remove('drag-over-top', 'drag-over-bottom');
                        
                        if (this !== draggedItem) {
                            const rect = this.getBoundingClientRect();
                            const dropPosition = e.clientY < (rect.top + rect.height / 2);
                            
                            if (dropPosition) {
                                // 放在目标元素之前
                                this.parentNode.insertBefore(draggedItem, this);
                            } else {
                                // 放在目标元素之后
                                this.parentNode.insertBefore(draggedItem, this.nextSibling);
                            }
                            saveOrder();
                        }
                    });
                });

                // 添加容器的拖拽处理
                container.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    const items = this.querySelectorAll('.search-item');
                    const lastItem = items[items.length - 1];
                    
                    // 检查是否拖到容器底部空白处
                    if (e.target === this) {
                        this.appendChild(draggedItem);
                        saveOrder();
                    }
                });
            }

            // 初始化时，如果本地存储中没有数据，保存初始数据
            if (!localStorage.getItem('allSearchItems')) {
                saveOrder();
            }
            
            // 加载保存的数据
            loadOrder();

            // 初始绑定事件
            bindDragEvents();

            container.addEventListener('dragover', function(e) {
                e.preventDefault();
            });

            // 添加新的链接相关代码
            const addButton = document.getElementById('addButton');
            const modal = document.getElementById('addModal');
            const closeBtn = document.querySelector('.close');
            const addLinkForm = document.getElementById('addLinkForm');

            // 打开模态框
            addButton.onclick = function() {
                modal.style.display = "block";
            }

            // 关闭模态框
            closeBtn.onclick = function() {
                modal.style.display = "none";
            }

            // 点击模态框外部关闭
            window.onclick = function(event) {
                if (event.target == modal) {
                    modal.style.display = "none";
                }
            }

            // 处理表单提交
            addLinkForm.onsubmit = function(e) {
                e.preventDefault();
                
                const itemData = {
                    text: document.getElementById('siteName').value,
                    href: document.getElementById('siteUrl').value,
                    description: document.getElementById('siteDescription').value
                };

                // 创建新的搜索项
                const newItem = createSearchItem(itemData);

                // 添加到容器
                container.appendChild(newItem);

                // 重新绑定拖拽事件
                bindDragEvents();

                // 保存新的排序
                saveOrder();

                // 清空表单并关闭模态框
                addLinkForm.reset();
                modal.style.display = "none";
            }

            // 创建搜索项的函数
            function createSearchItem(itemData) {
                const newItem = document.createElement('div');
                newItem.className = 'search-item';
                newItem.draggable = true;
                newItem.innerHTML = `
                    <a href="${itemData.href}" target="_blank">${itemData.text}</a>
                    <div class="description">${itemData.description}</div>
                    <button class="delete-btn" title="删除">×</button>
                `;

                // 添加删除功能
                const deleteBtn = newItem.querySelector('.delete-btn');
                deleteBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    if (confirm('确定要删除这个链接吗？')) {
                        newItem.remove();
                        saveOrder(); // 保存更改
                    }
                });

                return newItem;
            }
        });
    </script>
</body>
</html> 