SELECT convert(varchar,Year([日期]))+'年'+convert(varchar,Month([日期]))+'月' AS 年月,
       [仓库],
       SUM([总价]) AS 总价汇总,
       SUM([单据总成本]) AS 成本汇总,
       SUM([总价]-[单据总成本]) AS 毛利汇总
FROM [zuoan].[dbo].[v_danju]
WHERE 备注 NOT LIKE '%廖琪%'
      AND 单据类型 IN ('销售', '退货')
      AND 仓库 != '总店'
GROUP BY convert(varchar,Year([日期]))+'年'+convert(varchar,Month([日期]))+'月', [仓库]
ORDER BY convert(varchar,Year([日期]))+'年'+convert(varchar,Month([日期]))+'月', [仓库]


SELECT CONVERT(varchar,Year([日期]))+'年'+RIGHT('0'+CONVERT(varchar,Month([日期])),2)+'月' AS 年月,
       [仓库],
       SUM([总价]) AS 总价汇总,
       SUM([单据总成本]) AS 成本汇总,
       SUM([总价]-[单据总成本]) AS 毛利汇总
FROM [zuoan].[dbo].[v_danju]
WHERE 备注 NOT LIKE '%廖琪%'
      AND 单据类型 IN ('销售', '退货')
      AND 仓库 != '总店'
GROUP BY CONVERT(varchar,Year([日期]))+'年'+RIGHT('0'+CONVERT(varchar,Month([日期])),2)+'月', [仓库]
ORDER BY CONVERT(varchar,Year([日期]))+'年'+RIGHT('0'+CONVERT(varchar,Month([日期])),2)+'月', [仓库]