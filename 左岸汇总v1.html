<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>左岸销售数据汇总</title>
    <!-- 引入 ECharts 文件 -->
    <script src="echarts.min.js"></script>
    <!-- 引入 Papa Parse 用于解析 CSV -->
    <script src="papaparse.min.js"></script>
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script> -->
  </head>
  <body>
    <!-- 为 ECharts 准备一个定义了宽高的 DOM -->
    <div id="main" style="width: 1500px;height:700px;"></div>

    <script type="text/javascript">
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById('main'));
      
      // 读取并解析CSV文件
      fetch('sql.csv')
        .then(response => response.text())
        .then(csvData => {
          // 解析CSV数据
          const results = Papa.parse(csvData, {
            header: false,
            skipEmptyLines: true
          }).data;

          // 处理数据
          const monthSet = new Set();
          const storeSet = new Set();
          const dataMap = new Map();

          // 遍历数据,收集所有月份和店铺
          results.forEach(row => {
            const [month, store, value] = row;
            monthSet.add(month);
            storeSet.add(store);
            
            // 使用月份和店铺作为键存储数据
            const key = `${month}-${store}`;
            dataMap.set(key, parseFloat(value));
          });

          // 转换为数组并排序
          const months = Array.from(monthSet).sort();
          const stores = Array.from(storeSet).sort();

          // 生成系列数据
          const series = months.map(month => {
            const data = stores.map(store => {
              const key = `${month}-${store}`;
              return dataMap.get(key) || 0;
            });

            return {
              name: month,
              type: 'bar',
              emphasis: { focus: 'series' },
              label: {
                show: true,
                position: 'top',
                formatter: function(params) {
                  // 当值为0时返回空字符串,不显示标签
                  return params.value > 0 ? params.value : '';
                },
                fontSize: 12,
                color: '#666'
              },
              data: data
            };
          });

          // 生成图例选中状态
          const selected = {};
          months.forEach(month => {
            selected[month] = false;
          });

          // 处理x轴数据,过滤掉所有月份数据都为0的店铺
          const validStores = stores.filter(store => {
            return months.some(month => {
              const key = `${month}-${store}`;
              return (dataMap.get(key) || 0) > 0;
            });
          });

          // 配置项
          const option = {
            title: {
              text: '左岸销售数据汇总',
              subtext: '按店铺统计各月份总额',
              left: 'center'
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            legend: {
              type: 'scroll',
              orient: 'vertical',
              right: 10,
              top: 'center',
              selected: selected,
              data: months,
              selectedMode: 'multiple'
            },
            grid: {
              left: '3%',
              right: '15%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: validStores,
              axisLabel: {
                interval: 0,
                rotate: 30
              }
            },
            yAxis: {
              type: 'value',
              name: '金额'
            },
            series: series
          };

          // 使用配置项显示图表
          myChart.setOption(option);

          // 监听图例选择事件
          myChart.on('legendselectchanged', function(params) {
            // 获取当前选中的月份
            const selectedMonths = Object.keys(params.selected).filter(month => params.selected[month]);
            
            // 过滤出在选中月份中有非0数据的店铺
            const validStores = stores.filter(store => {
              return selectedMonths.some(month => {
                const key = `${month}-${store}`;
                return (dataMap.get(key) || 0) > 0;
              });
            });

            // 更新系列数据
            const newSeries = selectedMonths.map(month => {
              const data = validStores.map(store => {
                const key = `${month}-${store}`;
                return dataMap.get(key) || 0;
              });

              return {
                name: month,
                type: 'bar',
                emphasis: { focus: 'series' },
                label: {
                  show: true,
                  position: 'top',
                  formatter: function(params) {
                    return params.value > 0 ? params.value : '';
                  },
                  fontSize: 12,
                  color: '#666'
                },
                data: data
              };
            });

            // 更新图表配置
            myChart.setOption({
              xAxis: {
                data: validStores
              },
              series: newSeries
            });
          });
        })
        .catch(error => console.error('Error loading the CSV file:', error));
    </script>
  </body>
</html> 