<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>左岸销售数据月度汇总</title>
    <script src="echarts.min.js"></script>
  </head>
  <body>
    <div id="main" style="width: 1600px;height:800px;"></div>

    <script type="text/javascript">
      var myChart = echarts.init(document.getElementById('main'));

      var option = {
        title: {
          text: '左岸销售数据月度汇总',
          subtext: '2021-2025年各月份数据',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['大冶吾悦左岸先生店', '特卖场', '吾悦广场', '银泰首题', '银泰左岸', '左岸', '左岸武昌大道店'],
          top: '30px'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['2021年01月', '2021年02月', '2021年03月', '2021年04月', '2021年05月', '2021年06月', 
                '2021年07月', '2021年08月', '2021年09月', '2021年10月', '2021年11月', '2021年12月',
                '2022年01月', '2022年02月', '2022年03月', '2022年04月', '2022年05月', '2022年06月',
                '2022年07月', '2022年08月', '2022年09月', '2022年10月', '2022年11月', '2022年12月',
                '2023年01月', '2023年02月', '2023年03月', '2023年04月', '2023年05月', '2023年06月',
                '2023年07月', '2023年08月', '2023年09月', '2023年10月', '2023年11月', '2023年12月',
                '2024年01月', '2024年02月', '2024年03月', '2024年04月', '2024年05月', '2024年06月',
                '2024年07月', '2024年08月', '2024年09月', '2024年10月', '2024年11月', '2024年12月',
                '2025年01月', '2025年02月'],
          axisLabel: {
            interval: 2,
            rotate: 45
          }
        },
        yAxis: {
          type: 'value',
          name: '金额'
        },
        series: [
          {
            name: '大冶吾悦左岸先生店',
            type: 'bar',
            data: [0,0,0,0,0,0,0,0,0,0,0,0,
                   0,0,0,0,0,0,0,0,0,0,0,0,
                   0,0,0,0,0,0,0,0,0,0,0,177513,
                   197474,208122,62740,46491,48492,33456,
                   32115,37708,50744,113392,65373,135923,
                   272952,40245]
          },
          {
            name: '特卖场',
            type: 'bar',
            data: [0,0,0,0,0,0,0,0,0,0,0,0,
                   0,0,0,0,0,0,0,0,0,0,0,0,
                   0,0,0,0,0,0,0,0,0,0,0,0,
                   0,0,0,0,0,0,
                   0,0,0,0,65158,58050,
                   109921,21188]
          },
          {
            name: '吾悦广场',
            type: 'bar',
            data: [0,0,0,0,0,0,0,0,0,0,0,0,
                   0,0,0,0,0,0,0,0,0,83375,145331,181441,
                   478814,103616,112021,120565,79196,80494,81146,57869,99102,116206,133077,190911,
                   227495,335253,99586,75162,74459,52370,
                   50991,54254,59301,130993,115706,137815,
                   348207,41028]
          },
          {
            name: '银泰首题',
            type: 'bar',
            data: [0,0,0,0,0,0,0,0,0,0,28112,136749,
                   267792,53627,40144,14690,16454,32406,28282,18183,34999,76476,34154,51663,
                   229981,27149,31483,19473,22195,15327,11767,12065,39316,28783,29683,46175,
                   51654,103614,17188,3874,18042,0,
                   0,0,0,0,0,0,
                   0,0]
          },
          {
            name: '银泰左岸',
            type: 'bar',
            data: [49270,247816,66219,96988,93303,80953,72807,37625,115211,170304,148373,280836,
                   525218,141274,98360,33890,80960,79463,58494,58104,70138,146382,87310,163958,
                   409922,78745,88373,76151,91836,51008,60660,53261,86552,109611,121822,166639,
                   174395,295209,61958,67411,66276,49938,
                   32258,36340,50911,125654,98858,159272,
                   268228,25576]
          },
          {
            name: '左岸',
            type: 'bar',
            data: [68116,211506,104083,126796,107216,85393,73823,43687,85142,197367,145776,226779,
                   308630,83098,135937,58102,114980,80207,56953,46334,63184,158398,92844,157272,
                   293815,57956,107944,90940,97430,59191,49857,47158,62082,80745,112903,117859,
                   135428,170180,85866,86130,62160,42026,
                   40119,38310,47030,134512,104224,135166,
                   194942,15402]
          },
          {
            name: '左岸武昌大道店',
            type: 'bar',
            data: [0,0,0,0,0,0,18477,38422,37393,98707,78566,122853,
                   249536,65887,69529,31714,54696,39089,38091,36154,37777,99423,53872,71313,
                   229297,51569,56081,51822,55755,30636,30729,23135,42274,42731,57126,97531,
                   112362,136906,43008,35303,29316,23419,
                   19772,17744,26936,68578,64713,70157,
                   117437,5950]
          }
        ]
      };

      myChart.setOption(option);
    </script>
  </body>
</html>