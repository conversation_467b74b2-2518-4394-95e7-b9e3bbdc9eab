<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <!-- 引入刚刚下载的 ECharts 文件 -->
    <script src="echarts.min.js"></script>
  </head>
<body>
  <!-- 为 ECharts 准备一个定义了宽高的 DOM -->
  <div id="main" style="width: 800px;height:600px;"></div>

    <script type="text/javascript">
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById('main'));

      // 指定图表的配置项和数据
option = {
  title: {
   text: '在数据集中设置数据:“对象数组”的格式'
   },
label: {
        position: 'top',
        distance: 10,
	show: true,
        backgroundColor: '#eee',
        borderColor: '#555',
        borderWidth: 2,
        borderRadius: 5,
        padding: 10,
        fontSize: 18,
        shadowBlur: 3,
        shadowColor: '#888',
        shadowOffsetX: 0,
        shadowOffsetY: 3,
        textBorderColor: '#000',
        textBorderWidth: 3,
        color: '#fff'
},
  legend: {},
  tooltip: {},
  dataset: {
    // 用 dimensions 指定了维度的顺序。直角坐标系中，如果 X 轴 type 为 category，
    // 默认把第一个维度映射到 X 轴上，后面维度映射到 Y 轴上。
    // 如果不指定 dimensions，也可以通过指定 series.encode
    // 完成映射，参见后文。
    dimensions: ['系列', '2015', '2016', '2017'],
    source: [
      { 系列: '分类1', '2015': 43.3, '2016': 85.8, '2017': 93.7 },
      { 系列: '分类2', '2015': 83.1, '2016': 73.4, '2017': 55.1 },
      { 系列: '分类3', '2015': 86.4, '2016': 65.2, '2017': 82.5 },
      { 系列: '分类4', '2015': 72.4, '2016': 53.9, '2017': 39.1 }
    ]
  },
  xAxis: { type: 'category' },
  yAxis: {},
  series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
};

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    </script>

</body>
</html>