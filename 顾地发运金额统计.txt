根据下面提供的sql,帮我整理写一个sql语句,按 发运行发运确认时间 字段的 年月统计  发运行事务处理金额,查询条件为 发运行发运确认时间 的 开始日期和 结束日期.
日期参数帮我写出来  2024-1-1 到 2024-2-1
:p_organization_id 等于82


--------------------------------根据发运确认日期统计发运金额----------------------------------
SELECT TO_CHAR(cux_om_base_html_pkg_ww.get_ship_date(
                                    wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    87), 'YYYY"年"MM"月"') AS ship_month,
       SUM(CASE 
           -- 标准订单
           WHEN ott.order_category_code = 'ORDER' THEN 
               NVL(wdd.requested_quantity * ool.unit_selling_price, 0)
           -- 退货订单    
           WHEN ott.order_category_code = 'RETURN' THEN 
               NVL(-rcv.primary_quantity * ool.unit_selling_price, 0)
           -- 特殊混合订单 - 使用行类别判断正负    
           WHEN ott.order_category_code = 'MIXED' 
                AND ool.flow_status_code != 'ENTERED' THEN 
               CASE 
                   WHEN ool.line_category_code = 'RETURN' THEN 
                       NVL(-ool.ordered_quantity * ool.unit_selling_price, 0)  -- 退货行为负
                   ELSE 
                       NVL(ool.ordered_quantity * ool.unit_selling_price, 0)   -- 其他为正
               END
           END) AS total_amount,
       -- 添加明细统计以便检查
       SUM(CASE WHEN ott.order_category_code = 'ORDER' THEN 
               NVL(wdd.requested_quantity * ool.unit_selling_price, 0) ELSE 0 END) as order_ship_amount,
       SUM(CASE WHEN ott.order_category_code = 'RETURN' THEN 
               NVL(-rcv.primary_quantity * ool.unit_selling_price, 0) ELSE 0 END) as return_amount,
       SUM(CASE 
           WHEN ott.order_category_code = 'MIXED' AND ool.flow_status_code != 'ENTERED' THEN 
               CASE 
                   WHEN ool.line_category_code = 'RETURN' THEN 
                       NVL(-ool.ordered_quantity * ool.unit_selling_price, 0)
                   ELSE 
                       NVL(ool.ordered_quantity * ool.unit_selling_price, 0)
               END
           ELSE 0 
       END) as mixed_amount,
       COUNT(*) as total_lines,
       SUM(CASE WHEN ott.order_category_code = 'ORDER' THEN 1 ELSE 0 END) as order_lines,
       SUM(CASE WHEN ott.order_category_code = 'RETURN' THEN 1 ELSE 0 END) as return_lines,
       SUM(CASE WHEN ott.order_category_code = 'MIXED' 
                AND ool.flow_status_code != 'ENTERED' THEN 1 
           ELSE 0 END) as mixed_lines
FROM ONT.OE_ORDER_HEADERS_ALL ooh
INNER JOIN ONT.OE_ORDER_LINES_ALL ool 
    ON ooh.header_id = ool.header_id
INNER JOIN ONT.OE_TRANSACTION_TYPES_ALL ott 
    ON ooh.order_type_id = ott.transaction_type_id
LEFT OUTER JOIN WSH.WSH_DELIVERY_DETAILS wdd  
    ON wdd.source_line_id = ool.line_id 
    AND wdd.released_status != 'D'
LEFT OUTER JOIN RCV_TRANSACTIONS rcv      
    ON rcv.oe_order_line_id = ool.line_id
WHERE ooh.org_id = 87
AND ool.flow_status_code <> 'CANCELLED'
AND ooh.ordered_date BETWEEN TO_DATE('2023-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                        AND TO_DATE('2024-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
AND cux_om_base_html_pkg_ww.get_ship_date(wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    87) >=
                   to_date('2024-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
AND cux_om_base_html_pkg_ww.get_ship_date(wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    87) <=
                   to_date('2024-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')

GROUP BY TO_CHAR(cux_om_base_html_pkg_ww.get_ship_date(wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    87), 'YYYY"年"MM"月"')
ORDER BY ship_month;

--------------------------------根据订单日期统计发运金额----------------------------------

SELECT TO_CHAR(ooh.ordered_date, 'YYYY"年"MM"月"') AS ship_month,
       SUM(CASE 
           -- 标准订单
           WHEN ott.order_category_code = 'ORDER' THEN 
               NVL(wdd.requested_quantity * ool.unit_selling_price, 0)
           -- 退货订单    
           WHEN ott.order_category_code = 'RETURN' THEN 
               NVL(-rcv.primary_quantity * ool.unit_selling_price, 0)
           -- 特殊混合订单 - 使用行类别判断正负    
           WHEN ott.order_category_code = 'MIXED' 
                AND ool.flow_status_code != 'ENTERED' THEN 
               CASE 
                   WHEN ool.line_category_code = 'RETURN' THEN 
                       NVL(-ool.ordered_quantity * ool.unit_selling_price, 0)  -- 退货行为负
                   ELSE 
                       NVL(ool.ordered_quantity * ool.unit_selling_price, 0)   -- 其他为正
               END
           END) AS total_amount,
       -- 添加明细统计以便检查
       SUM(CASE WHEN ott.order_category_code = 'ORDER' THEN 
               NVL(wdd.requested_quantity * ool.unit_selling_price, 0) ELSE 0 END) as order_amount,
       SUM(CASE WHEN ott.order_category_code = 'RETURN' THEN 
               NVL(-rcv.primary_quantity * ool.unit_selling_price, 0) ELSE 0 END) as return_amount,
       SUM(CASE 
           WHEN ott.order_category_code = 'MIXED' AND ool.flow_status_code != 'ENTERED' THEN 
               CASE 
                   WHEN ool.line_category_code = 'RETURN' THEN 
                       NVL(-ool.ordered_quantity * ool.unit_selling_price, 0)
                   ELSE 
                       NVL(ool.ordered_quantity * ool.unit_selling_price, 0)
               END
           ELSE 0 
       END) as mixed_amount,
       COUNT(*) as total_lines,
       SUM(CASE WHEN ott.order_category_code = 'ORDER' THEN 1 ELSE 0 END) as order_lines,
       SUM(CASE WHEN ott.order_category_code = 'RETURN' THEN 1 ELSE 0 END) as return_lines,
       SUM(CASE WHEN ott.order_category_code = 'MIXED' 
                AND ool.flow_status_code != 'ENTERED' THEN 1 
           ELSE 0 END) as mixed_lines
FROM ONT.OE_ORDER_HEADERS_ALL ooh
INNER JOIN ONT.OE_ORDER_LINES_ALL ool 
    ON ooh.header_id = ool.header_id
INNER JOIN ONT.OE_TRANSACTION_TYPES_ALL ott 
    ON ooh.order_type_id = ott.transaction_type_id
LEFT OUTER JOIN WSH.WSH_DELIVERY_DETAILS wdd  
    ON wdd.source_line_id = ool.line_id 
    AND wdd.released_status != 'D'
LEFT OUTER JOIN RCV_TRANSACTIONS rcv      
    ON rcv.oe_order_line_id = ool.line_id
WHERE ooh.org_id = 82
AND ool.flow_status_code <> 'CANCELLED'
AND ooh.ordered_date BETWEEN TO_DATE('2024-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                        AND TO_DATE('2024-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
GROUP BY TO_CHAR(ooh.ordered_date, 'YYYY"年"MM"月"')
ORDER BY ship_month;
---------------------------------------------------
------------以下作废------------------------------
订单金额
SELECT TO_CHAR(ooh.ordered_date, 'YYYY"年"MM"月"') AS ship_month,
       SUM(CASE 
           WHEN ott.order_category_code = 'ORDER' THEN 
               NVL(ool.ordered_quantity * ool.unit_selling_price, 0)
           WHEN ott.order_category_code = 'RETURN' THEN 
               NVL(-ool.ordered_quantity * ool.unit_selling_price, 0)
           END) AS total_amount,
       COUNT(*) as order_line_count
FROM ONT.OE_ORDER_HEADERS_ALL ooh
INNER JOIN ONT.OE_ORDER_LINES_ALL ool ON ooh.header_id = ool.header_id
INNER JOIN ONT.OE_TRANSACTION_TYPES_ALL ott ON ooh.order_type_id = ott.transaction_type_id
WHERE ooh.org_id = 82 AND 
ool.flow_status_code <> 'CANCELLED' AND 
ooh.ordered_date BETWEEN TO_DATE('2023-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                        AND TO_DATE('2023-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
GROUP BY TO_CHAR(ooh.ordered_date, 'YYYY"年"MM"月"')
ORDER BY ship_month;

发货金额
SELECT TO_CHAR(ooh.ordered_date, 'YYYY"年"MM"月"') AS ship_month,
       SUM(CASE 
           WHEN ott.order_category_code = 'ORDER' THEN 
               NVL(wdd.requested_quantity * ool.unit_selling_price, 0)  -- 发运数量
           WHEN ott.order_category_code = 'RETURN' THEN 
               NVL(-rcv.primary_quantity * ool.unit_selling_price, 0)   -- 退货数量
           END) AS total_amount,
       COUNT(*) as line_count
FROM ONT.OE_ORDER_HEADERS_ALL ooh
INNER JOIN ONT.OE_ORDER_LINES_ALL ool 
    ON ooh.header_id = ool.header_id
INNER JOIN ONT.OE_TRANSACTION_TYPES_ALL ott 
    ON ooh.order_type_id = ott.transaction_type_id
LEFT OUTER JOIN WSH.WSH_DELIVERY_DETAILS wdd  -- 发运明细表
    ON wdd.source_line_id = ool.line_id 
    AND wdd.released_status != 'D'
LEFT OUTER JOIN RCV_TRANSACTIONS rcv      -- 退货事务表
    ON rcv.oe_order_line_id = ool.line_id
WHERE ooh.org_id = 82
AND ool.flow_status_code <> 'CANCELLED'
AND ooh.ordered_date BETWEEN TO_DATE('2024-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                        AND TO_DATE('2024-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
GROUP BY TO_CHAR(ooh.ordered_date, 'YYYY"年"MM"月"')
ORDER BY ship_month;


订单类型
湖北销售价格调整
