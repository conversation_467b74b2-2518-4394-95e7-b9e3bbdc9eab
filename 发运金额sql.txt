SELECT TO_CHAR(cux_om_base_html_pkg_ww.get_ship_date(
                                    wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    82), 'YYYY"年"MM"月"') AS ship_month,
       SUM(CASE 
           -- 标准订单
           WHEN ott.order_category_code = 'ORDER' THEN 
               NVL(wdd.requested_quantity * ool.unit_selling_price, 0)
           -- 退货订单    
           WHEN ott.order_category_code = 'RETURN' THEN 
               NVL(-rcv.primary_quantity * ool.unit_selling_price, 0)
           -- 特殊混合订单 - 使用行类别判断正负    
           WHEN ott.order_category_code = 'MIXED' 
                AND ool.flow_status_code != 'ENTERED' THEN 
               CASE 
                   WHEN ool.line_category_code = 'RETURN' THEN 
                       NVL(-ool.ordered_quantity * ool.unit_selling_price, 0)  -- 退货行为负
                   ELSE 
                       NVL(ool.ordered_quantity * ool.unit_selling_price, 0)   -- 其他为正
               END
           END) AS total_amount,
       -- 添加明细统计以便检查
       SUM(CASE WHEN ott.order_category_code = 'ORDER' THEN 
               NVL(wdd.requested_quantity * ool.unit_selling_price, 0) ELSE 0 END) as order_ship_amount,
       SUM(CASE WHEN ott.order_category_code = 'RETURN' THEN 
               NVL(-rcv.primary_quantity * ool.unit_selling_price, 0) ELSE 0 END) as return_amount,
       SUM(CASE 
           WHEN ott.order_category_code = 'MIXED' AND ool.flow_status_code != 'ENTERED' THEN 
               CASE 
                   WHEN ool.line_category_code = 'RETURN' THEN 
                       NVL(-ool.ordered_quantity * ool.unit_selling_price, 0)
                   ELSE 
                       NVL(ool.ordered_quantity * ool.unit_selling_price, 0)
               END
           ELSE 0 
       END) as mixed_amount,
       COUNT(*) as total_lines,
       SUM(CASE WHEN ott.order_category_code = 'ORDER' THEN 1 ELSE 0 END) as order_lines,
       SUM(CASE WHEN ott.order_category_code = 'RETURN' THEN 1 ELSE 0 END) as return_lines,
       SUM(CASE WHEN ott.order_category_code = 'MIXED' 
                AND ool.flow_status_code != 'ENTERED' THEN 1 
           ELSE 0 END) as mixed_lines
FROM ONT.OE_ORDER_HEADERS_ALL ooh
INNER JOIN ONT.OE_ORDER_LINES_ALL ool 
    ON ooh.header_id = ool.header_id
INNER JOIN ONT.OE_TRANSACTION_TYPES_ALL ott 
    ON ooh.order_type_id = ott.transaction_type_id
LEFT OUTER JOIN WSH.WSH_DELIVERY_DETAILS wdd  
    ON wdd.source_line_id = ool.line_id 
    AND wdd.released_status != 'D'
LEFT OUTER JOIN RCV_TRANSACTIONS rcv      
    ON rcv.oe_order_line_id = ool.line_id
WHERE ooh.org_id = 82
AND ool.flow_status_code <> 'CANCELLED'
AND ooh.ordered_date BETWEEN TO_DATE('2023-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                        AND TO_DATE('2025-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
AND cux_om_base_html_pkg_ww.get_ship_date(wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    82) >=
                   to_date('2024-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
AND cux_om_base_html_pkg_ww.get_ship_date(wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    82) <=
                   to_date('2024-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')

GROUP BY TO_CHAR(cux_om_base_html_pkg_ww.get_ship_date(wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    82), 'YYYY"年"MM"月"')
ORDER BY ship_month;