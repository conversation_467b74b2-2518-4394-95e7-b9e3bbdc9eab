-- 先删除已存在的存储过程（如果存在）
BEGIN
    EXECUTE IMMEDIATE 'DROP PROCEDURE apps.proc_update_t_shouhuo';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -4043 THEN  -- -4043 是对象不存在的错误码
            RAISE;
        END IF;
END;
/

-- 创建存储过程
CREATE OR REPLACE PROCEDURE apps.proc_update_t_shouhuo
(
    errbuf          OUT NOCOPY VARCHAR2,   -- 标准错误缓冲
    retcode         OUT NOCOPY NUMBER      -- 标准返回代码
)
IS
BEGIN
    -- 初始化返回值
    retcode := 0;
    errbuf := NULL;
    
    BEGIN
        -- 先清空表
        EXECUTE IMMEDIATE 'TRUNCATE TABLE apps.t_shouhuo';
        
        -- 插入数据
        INSERT INTO apps.t_shouhuo (
            ship_month,
            order_ship_amount,
            return_amount,
            mixed_amount,
            ship_amount,
            total_lines,
            order_lines,
            return_lines,
            mixed_lines
        )
        WITH return_data AS (
            SELECT TO_CHAR(CASE 
                            WHEN ottl.order_category_code = 'RETURN' THEN 
                                rcv.transaction_date
                            ELSE 
                                cux_om_base_html_pkg_ww.get_ship_date(
                                    wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    82)
                        END, 'YYYY"年"MM"月"') AS ship_month,
                SUM(CASE 
                    WHEN ottl.order_category_code = 'RETURN' AND rcv.transaction_date IS NOT NULL THEN 
                        NVL(-rcv.primary_quantity * ool.unit_selling_price, 0) 
                    ELSE 0 
                END) as return_amount,
                SUM(CASE 
                    WHEN ottl.order_category_code = 'RETURN' AND rcv.transaction_date IS NOT NULL THEN 1 
                    ELSE 0 
                END) as return_lines
            FROM ONT.OE_ORDER_HEADERS_ALL ooh
            INNER JOIN ONT.OE_ORDER_LINES_ALL ool ON ooh.header_id = ool.header_id
            INNER JOIN ONT.OE_TRANSACTION_TYPES_ALL ott ON ooh.order_type_id = ott.transaction_type_id
            INNER JOIN ONT.OE_TRANSACTION_TYPES_ALL ottl ON ool.line_type_id = ottl.transaction_type_id
            LEFT OUTER JOIN WSH.WSH_DELIVERY_DETAILS wdd ON wdd.source_line_id = ool.line_id AND wdd.released_status != 'D'
            LEFT OUTER JOIN RCV_TRANSACTIONS rcv ON rcv.oe_order_line_id = ool.line_id AND rcv.transaction_type = 'DELIVER'
            WHERE ooh.org_id = 82
            AND ool.flow_status_code <> 'CANCELLED'
            AND ooh.ordered_date BETWEEN TO_DATE('2023-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                                    AND TO_DATE('2025-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
            AND (
                (ottl.order_category_code = 'RETURN' AND 
                rcv.transaction_date BETWEEN TO_DATE('2024-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                                        AND TO_DATE('2024-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'))
                OR 
                (ottl.order_category_code != 'RETURN' AND
                cux_om_base_html_pkg_ww.get_ship_date(wdd.delivery_detail_id,
                                                wdd.released_status,
                                                ott.order_category_code,
                                                ott.transaction_type_id,
                                                ooh.flow_status_code,
                                                ooh.ordered_date,
                                                null,
                                                ool.inventory_item_id,
                                                82) BETWEEN TO_DATE('2024-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                                                    AND TO_DATE('2024-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS'))
            )
            GROUP BY TO_CHAR(CASE 
                            WHEN ottl.order_category_code = 'RETURN' THEN 
                                rcv.transaction_date
                            ELSE 
                                cux_om_base_html_pkg_ww.get_ship_date(
                                    wdd.delivery_detail_id,
                                    wdd.released_status,
                                    ott.order_category_code,
                                    ott.transaction_type_id,
                                    ooh.flow_status_code,
                                    ooh.ordered_date,
                                    null,
                                    ool.inventory_item_id,
                                    82)
                        END, 'YYYY"年"MM"月"')
        ),
        other_data AS (
            SELECT TO_CHAR(cux_om_base_html_pkg_ww.get_ship_date(
                                        wdd.delivery_detail_id,
                                        wdd.released_status,
                                        ott.order_category_code,
                                        ott.transaction_type_id,
                                        ooh.flow_status_code,
                                        ooh.ordered_date,
                                        null,
                                        ool.inventory_item_id,
                                        82), 'YYYY"年"MM"月"') AS ship_month,
                SUM(CASE WHEN ott.order_category_code = 'ORDER' THEN 
                        NVL(wdd.requested_quantity * ool.unit_selling_price, 0) ELSE 0 END) as order_ship_amount,
                SUM(CASE 
                    WHEN ott.order_category_code = 'MIXED' AND ool.flow_status_code != 'ENTERED' THEN 
                        CASE 
                            WHEN ool.line_category_code = 'RETURN' THEN 
                                NVL(-ool.ordered_quantity * ool.unit_selling_price, 0)
                            ELSE 
                                NVL(ool.ordered_quantity * ool.unit_selling_price, 0)
                        END
                    ELSE 0 
                END) as mixed_amount,
                COUNT(*) as total_lines,
                SUM(CASE WHEN ott.order_category_code = 'ORDER' THEN 1 ELSE 0 END) as order_lines,
                SUM(CASE WHEN ott.order_category_code = 'MIXED' 
                            AND ool.flow_status_code != 'ENTERED' THEN 1 
                        ELSE 0 END) as mixed_lines
            FROM ONT.OE_ORDER_HEADERS_ALL ooh
            INNER JOIN ONT.OE_ORDER_LINES_ALL ool ON ooh.header_id = ool.header_id
            INNER JOIN ONT.OE_TRANSACTION_TYPES_ALL ott ON ooh.order_type_id = ott.transaction_type_id
            LEFT OUTER JOIN WSH.WSH_DELIVERY_DETAILS wdd ON wdd.source_line_id = ool.line_id AND wdd.released_status != 'D'
            WHERE ooh.org_id = 82
            AND ool.flow_status_code <> 'CANCELLED'
            AND ooh.ordered_date BETWEEN TO_DATE('2023-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                                    AND TO_DATE('2025-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
            AND cux_om_base_html_pkg_ww.get_ship_date(wdd.delivery_detail_id,
                                                wdd.released_status,
                                                ott.order_category_code,
                                                ott.transaction_type_id,
                                                ooh.flow_status_code,
                                                ooh.ordered_date,
                                                null,
                                                ool.inventory_item_id,
                                                82) BETWEEN TO_DATE('2024-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                                                    AND TO_DATE('2024-12-31 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
            GROUP BY TO_CHAR(cux_om_base_html_pkg_ww.get_ship_date(
                                                wdd.delivery_detail_id,
                                                wdd.released_status,
                                                ott.order_category_code,
                                                ott.transaction_type_id,
                                                ooh.flow_status_code,
                                                ooh.ordered_date,
                                                null,
                                                ool.inventory_item_id,
                                                82), 'YYYY"年"MM"月"')
        )
        SELECT COALESCE(r.ship_month, o.ship_month) as ship_month,
               NVL(o.order_ship_amount, 0) as order_ship_amount,
               NVL(r.return_amount, 0) as return_amount,
               NVL(o.mixed_amount, 0) as mixed_amount,
               NVL(o.order_ship_amount, 0) + NVL(r.return_amount, 0) + NVL(o.mixed_amount, 0) as ship_amount,
               NVL(o.order_lines, 0) + NVL(r.return_lines, 0) + NVL(o.mixed_lines, 0) as total_lines,
               NVL(o.order_lines, 0) as order_lines,
               NVL(r.return_lines, 0) as return_lines,
               NVL(o.mixed_lines, 0) as mixed_lines
        FROM return_data r
        FULL OUTER JOIN other_data o ON r.ship_month = o.ship_month
        ORDER BY ship_month;

        COMMIT;
        
        -- 添加成功日志
        fnd_file.put_line(fnd_file.log, '成功更新t_shouhuo表数据');
        
    EXCEPTION
        WHEN OTHERS THEN
            errbuf := SUBSTR('更新t_shouhuo表时发生错误: ' || SQLERRM, 1, 2000);
            retcode := 2;
            fnd_file.put_line(fnd_file.log, errbuf);
            ROLLBACK;
    END;
END proc_update_t_shouhuo;
/

-- 授予执行权限
GRANT EXECUTE ON apps.proc_update_t_shouhuo TO INV;
GRANT EXECUTE ON apps.proc_update_t_shouhuo TO ONT;
GRANT EXECUTE ON apps.proc_update_t_shouhuo TO WSH;

-- 编译存储过程
ALTER PROCEDURE apps.proc_update_t_shouhuo COMPILE;

-- 检查编译状态
SELECT object_name, status 
FROM all_objects 
WHERE object_name = 'PROC_UPDATE_T_SHOUHUO' 
AND owner = 'APPS';
/ 