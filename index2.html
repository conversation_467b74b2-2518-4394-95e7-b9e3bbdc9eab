<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <!-- 引入刚刚下载的 ECharts 文件 -->
    <script src="echarts.min.js"></script>
  </head>
<body>
  <!-- 为 ECharts 准备一个定义了宽高的 DOM -->
  <div id="main" style="width: 800px;height:600px;"></div>

    <script type="text/javascript">
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById('main'));

      // 指定图表的配置项和数据
option = {
  title: {
   text: '在数据集中设置数据'
   },
label: {
        position: 'top',
        distance: 10,
	show: true,
        backgroundColor: '#eee',
        borderColor: '#555',
        borderWidth: 2,
        borderRadius: 5,
        padding: 10,
        fontSize: 18,
        shadowBlur: 3,
        shadowColor: '#888',
        shadowOffsetX: 0,
        shadowOffsetY: 3,
        textBorderColor: '#000',
        textBorderWidth: 3,
        color: '#fff'
},
  legend: {},
  tooltip: {},
  dataset: {
    // 提供一份数据。
    source: [
      ['系列', '2015', '2016', '2017'],
      ['分类1', 43.3, 85.8, 93.7],
      ['分类2', 83.1, 73.4, 55.1],
      ['分类3', 86.4, 65.2, 82.5],
      ['分类4', 72.4, 53.9, 39.1]
    ]
  },
  // 声明一个 X 轴，类目轴（category）。默认情况下，类目轴对应到 dataset 第一列。
  xAxis: { type: 'category' },
  // 声明一个 Y 轴，数值轴。
  yAxis: {},
  // 声明多个 bar 系列，默认情况下，每个系列会自动对应到 dataset 的每一列。
  series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
};

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    </script>

</body>
</html>