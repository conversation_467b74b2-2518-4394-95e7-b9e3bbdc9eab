<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>左岸销售数据月度汇总</title>
    <script src="echarts.min.js"></script>
  </head>
  <body>
    <div id="main" style="width: 1600px;height:800px;"></div>

    <script type="text/javascript">
      var myChart = echarts.init(document.getElementById('main'));

      var option = {
        title: {
          text: '左岸销售数据月度汇总',
          subtext: '2021-2024年各月份数据',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['总额', '成本', '利润'],
          top: '30px'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['大冶吾悦左岸先生店', '吾悦广场', '银泰首题', '银泰左岸', '左岸', '左岸武昌大道店'],
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value',
          name: '金额'
        },
        series: [
          {
            name: '总额',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
              position: 'inside'
            },
            data: [774287, 2468528, 1226030, 3040825, 2365870, 1447675]
          },
          {
            name: '成本',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
              position: 'inside'
            },
            data: [225660, 656500, 359830, 873508, 650861, 485586]
          },
          {
            name: '利润',
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
              position: 'inside'
            },
            data: [548627, 1812028, 866200, 2167317, 1715009, 962089]
          }
        ]
      };

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    </script>
  </body>
</html> 